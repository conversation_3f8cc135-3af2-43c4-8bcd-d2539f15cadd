# Authorizer Function Improvements

## Overview
The authorizer function has been improved to be more minimal, defensive, and robust against malformed input.

## Key Improvements Made

### 1. Defensive Token Extraction
**Before:**
```javascript
const token = event.headers?.authorization?.split(' ')[1];
```

**After:**
```javascript
function extractToken(authHeader) {
  if (!authHeader || typeof authHeader !== 'string') {
    return null;
  }
  
  const parts = authHeader.split(' ');
  if (parts.length !== 2 || parts[0].toLowerCase() !== 'bearer') {
    return null;
  }
  
  return parts[1];
}
```

**Benefits:**
- Validates authorization header format
- Ensures exactly 2 parts (Bearer + token)
- Case-insensitive Bearer check
- Handles null/undefined/non-string headers gracefully

### 2. Safe Method ARN Parsing
**Before:**
```javascript
const resourceParts = methodArn.split('/');
const apiPath = resourceParts.slice(5).join('/');
const stage = resourceParts[4];
```

**After:**
```javascript
function parseMethodArn(methodArn) {
  if (!methodArn || typeof methodArn !== 'string') {
    throw new Error('Invalid methodArn');
  }
  
  const parts = methodArn.split('/');
  if (parts.length < 6) {
    throw new Error('Malformed methodArn');
  }
  
  return {
    stage: parts[4],
    apiPath: parts.slice(5).join('/')
  };
}
```

**Benefits:**
- Validates methodArn exists and is a string
- Ensures minimum required parts are present
- Throws descriptive errors for debugging

### 3. Consistent Error Handling
**Before:**
```javascript
if (!token) {
  throw new Error('Unauthorized');
}
// ... other places that throw errors
```

**After:**
```javascript
function createDenyPolicy(principalId = 'user') {
  return {
    principalId,
    policyDocument: {
      Version: '2012-10-17',
      Statement: [{
        Action: 'execute-api:Invoke',
        Effect: 'Deny',
        Resource: '*'
      }]
    }
  };
}

// All errors now return deny policies instead of throwing
```

**Benefits:**
- AWS API Gateway expects policy responses, not thrown errors
- Consistent behavior for all error conditions
- Better debugging with logged error messages

### 4. Environment Variable Validation
**Added:**
```javascript
if (!AUTH0_DOMAIN || !AUTH0_AUDIENCE) {
  throw new Error('Missing required environment variables: AUTH0_DOMAIN and AUTH0_AUDIENCE');
}
```

**Benefits:**
- Fail fast if configuration is missing
- Clear error message for deployment issues

### 5. Enhanced JWKS Client Configuration
**Before:**
```javascript
const client = jwksClient({
  jwksUri: `https://${AUTH0_DOMAIN}/.well-known/jwks.json`
});
```

**After:**
```javascript
const client = jwksClient({
  jwksUri: `https://${AUTH0_DOMAIN}/.well-known/jwks.json`,
  cache: true,
  cacheMaxAge: 600000, // 10 minutes
  rateLimit: true,
  jwksRequestsPerMinute: 5
});
```

**Benefits:**
- Caching reduces latency and external calls
- Rate limiting prevents abuse
- Better performance for high-traffic scenarios

### 6. Improved Claims Validation
**Before:**
```javascript
const customer = claims['customer'];
const roles = claims['roles'] || [];
```

**After:**
```javascript
const customer = decoded.customer;
const roles = decoded.roles || [];
const principalId = decoded.sub;

if (!customer || !principalId) {
  console.log('Missing required claims: customer or sub');
  return createDenyPolicy();
}
```

**Benefits:**
- Validates required claims are present
- Fails gracefully if claims are missing
- Logs issues for debugging

### 7. Enhanced Key Validation
**Before:**
```javascript
function getKey(header, callback) {
  client.getSigningKey(header.kid, (err, key) => {
    // ...
  });
}
```

**After:**
```javascript
function getKey(header, callback) {
  if (!header?.kid) {
    return callback(new Error('Missing key ID in token header'));
  }
  
  client.getSigningKey(header.kid, (err, key) => {
    // ...
  });
}
```

**Benefits:**
- Validates key ID exists before attempting to fetch
- Clearer error messages for debugging

## Security Considerations

1. **No sensitive data in logs** - Only logs non-sensitive information for debugging
2. **Fail closed** - All error conditions result in deny policies
3. **Input validation** - All inputs are validated before processing
4. **Rate limiting** - JWKS requests are rate limited to prevent abuse

## Testing

Run the test file to verify defensive behavior:
```bash
cd functions/auth
node test-authorizer.js
```

The test file validates handling of:
- Malformed authorization headers
- Invalid methodArn formats
- Missing required fields
- Type validation

## Migration Notes

The improved authorizer is backward compatible with existing valid requests. Only malformed or invalid requests will now be properly rejected instead of causing runtime errors.
