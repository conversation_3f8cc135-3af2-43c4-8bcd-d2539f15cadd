const jwt = require('jsonwebtoken');
const jwksClient = require('jwks-rsa');

const AUTH0_DOMAIN = process.env.AUTH0_DOMAIN;
const AUTH0_AUDIENCE = process.env.AUTH0_AUDIENCE;

const client = jwksClient({
  jwksUri: `https://${AUTH0_DOMAIN}/.well-known/jwks.json`
});

function getKey(header, callback) {
  client.getSigningKey(header.kid, (err, key) => {
    if (err) {
      callback(err);
    } else {
      const signingKey = key.getPublicKey();
      callback(null, signingKey);
    }
  });
}

exports.handler = async (event) => {
  const token = event.headers?.authorization?.split(' ')[1];

  if (!token) {
    throw new Error('Unauthorized');
  }

  const decoded = await new Promise((resolve, reject) => {
    jwt.verify(token, getKey, {
      audience: AUTH0_AUDIENCE,
      issuer: `https://${AUTH0_DOMAIN}/`,
      algorithms: ['RS256'],
    }, (err, decoded) => {
      if (err) return reject(err);
      resolve(decoded);
    });
  });

  const claims = decoded;
  const customer = claims['customer'];
  const roles = claims['roles'] || [];

  const methodArn = event.methodArn;

  // Simple example permission logic
  const allowedResources = [];
  const resourceParts = methodArn.split('/');
  const apiPath = resourceParts.slice(5).join('/');
  const stage = resourceParts[4];

  // Example: GET /reports/{customer}/{site}
  if (apiPath.startsWith(`GET/reports/${customer}`)) {
    allowedResources.push(`${methodArn}`);
  }

  if (allowedResources.length === 0) {
    throw new Error('Unauthorized');
  }

  return {
    principalId: claims.sub,
    policyDocument: {
      Version: '2012-10-17',
      Statement: [
        {
          Action: 'execute-api:Invoke',
          Effect: 'Allow',
          Resource: allowedResources,
        }
      ]
    },
    context: {
      roles: JSON.stringify(roles),
      customer: customer
    }
  };
};
