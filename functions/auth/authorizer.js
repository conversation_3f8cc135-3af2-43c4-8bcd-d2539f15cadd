const jwt = require('jsonwebtoken');
const jwksClient = require('jwks-rsa');

// Validate required environment variables
const AUTH0_DOMAIN = process.env.AUTH0_DOMAIN;
const AUTH0_AUDIENCE = process.env.AUTH0_AUDIENCE;

if (!AUTH0_DOMAIN || !AUTH0_AUDIENCE) {
  throw new Error('Missing required environment variables: AUTH0_DOMAIN and AUTH0_AUDIENCE');
}

const client = jwksClient({
  jwksUri: `https://${AUTH0_DOMAIN}/.well-known/jwks.json`,
  cache: true,
  cacheMaxAge: 600000, // 10 minutes
  rateLimit: true,
  jwksRequestsPerMinute: 5
});

function getKey(header, callback) {
  if (!header?.kid) {
    return callback(new Error('Missing key ID in token header'));
  }

  client.getSigningKey(header.kid, (err, key) => {
    if (err) {
      callback(err);
    } else {
      const signingKey = key.getPublicKey();
      callback(null, signingKey);
    }
  });
}

function extractToken(authHeader) {
  if (!authHeader || typeof authHeader !== 'string') {
    return null;
  }

  const parts = authHeader.split(' ');
  if (parts.length !== 2 || parts[0].toLowerCase() !== 'bearer') {
    return null;
  }

  return parts[1];
}

function parseMethodArn(methodArn) {
  if (!methodArn || typeof methodArn !== 'string') {
    throw new Error('Invalid methodArn');
  }

  const parts = methodArn.split('/');
  if (parts.length < 6) {
    throw new Error('Malformed methodArn');
  }

  return {
    stage: parts[4],
    apiPath: parts.slice(5).join('/')
  };
}

function createDenyPolicy(principalId = 'user') {
  return {
    principalId,
    policyDocument: {
      Version: '2012-10-17',
      Statement: [{
        Action: 'execute-api:Invoke',
        Effect: 'Deny',
        Resource: '*'
      }]
    }
  };
}

exports.handler = async (event) => {
  try {
    // Extract and validate token
    const token = extractToken(event.headers?.authorization);
    if (!token) {
      console.log('No valid token found in authorization header');
      return createDenyPolicy();
    }

    // Verify JWT token
    const decoded = await new Promise((resolve, reject) => {
      jwt.verify(token, getKey, {
        audience: AUTH0_AUDIENCE,
        issuer: `https://${AUTH0_DOMAIN}/`,
        algorithms: ['RS256'],
      }, (err, decoded) => {
        if (err) return reject(err);
        resolve(decoded);
      });
    });

    // Extract claims with defaults
    const customer = decoded.customer;
    const roles = decoded.roles || [];
    const principalId = decoded.sub;

    if (!customer || !principalId) {
      console.log('Missing required claims: customer or sub');
      return createDenyPolicy();
    }

    // Parse method ARN safely
    const { apiPath } = parseMethodArn(event.methodArn);

    // Simplified permission logic - allow access to customer's own resources
    const allowedResources = [];
    if (apiPath.startsWith(`GET/reports/${customer}`) ||
        apiPath.startsWith(`POST/reports/${customer}`) ||
        apiPath.startsWith(`PUT/reports/${customer}`) ||
        apiPath.startsWith(`DELETE/reports/${customer}`)) {
      allowedResources.push(event.methodArn);
    }

    if (allowedResources.length === 0) {
      console.log(`Access denied for customer ${customer} to ${apiPath}`);
      return createDenyPolicy(principalId);
    }

    return {
      principalId,
      policyDocument: {
        Version: '2012-10-17',
        Statement: [{
          Action: 'execute-api:Invoke',
          Effect: 'Allow',
          Resource: allowedResources,
        }]
      },
      context: {
        roles: JSON.stringify(roles),
        customer
      }
    };

  } catch (error) {
    console.error('Authorization error:', error.message);
    return createDenyPolicy();
  }
};
